from smolagents import CodeAgent, LiteLLMModel,AzureOpenAIServerModel,DuckDuckGoSearchTool, VisitWebpageTool, HfApiModel,load_tool,tool
import datetime
import requests
import pytz
import yaml
from serp_models import Serp<PERSON><PERSON>ult
from serp_parser import parse_serp_result
from tools.final_answer import FinalAnswerTool
from tools.dataforseo_keyword_research import dataforseo_keyword_research
from tools.enhanced_dataforseo_mcp_client import (
    
    smart_keyword_research_workflow,
    advanced_serp_based_clustering,
    comprehensive_website_analysis
)
from tools.autonomous_mcp_agent import autonomous_mcp_call
from tools.direct_dataforseo_client import (
    serp_google_organic_live,
    labs_google_related_keywords,
    labs_google_keyword_ideas,
    backlinks_summary,
    keywords_google_ads_search_volume,
    labs_google_domain_rank_overview,
    labs_google_ranked_keywords,
    call_dataforseo
)
from Gradio_UI import GradioUI

# Below is an example of a tool that does nothing. Amaze us with your creativity !
@tool
def my_custom_tool(arg1:str, arg2:int)-> str: #it's import to specify the return type
    #Keep this format for the description / args / args description but feel free to modify the tool
    """A tool that does nothing yet 
    Args:
        arg1: the first argument
        arg2: the second argument
    """
    return "What magic will you build ?"

@tool
def get_current_time_in_timezone(timezone: str) -> str:
    """A tool that fetches the current local time in a specified timezone.
    Args:
        timezone: A string representing a valid timezone (e.g., 'America/New_York').
    """
    try:
        # Create timezone object
        tz = pytz.timezone(timezone)
        # Get current time in that timezone
        local_time = datetime.datetime.now(tz).strftime("%Y-%m-%d %H:%M:%S")
        return f"The current local time in {timezone} is: {local_time}"
    except Exception as e:
        return f"Error fetching time for timezone '{timezone}': {str(e)}"



@tool
def googleSearch(query: str, location: str = "United States" ) ->SerpResult:
    """ Performs a google web search based on your query then returns the top search results

     Args:
        query: Query to search 
        location: Geographic location for the search in full name eg: United States
    
    Returns:
        SerpResult object containing the search results
    """
    import requests
    import json
    url = "https://api.dataforseo.com/v3/serp/google/organic/live/advanced"
    data = [{"keyword": query, "location_name": location, "language_code": "en", "device": "desktop", "os": "windows"}]
    #payload=f"[{keyword:{keyword}, location_code:2826, language_code:en, device:desktop, os:windows, depth:100}]"
    payload = json.dumps(data)
    # print(payload)
    headers = {
        'Authorization': 'Basic  ****************************************************',
        'Content-Type': 'application-json'
    }

    try:
        response = requests.request("POST", url, headers=headers, data=payload)
        all_responses =  json.loads(response.text)
        result = parse_serp_result(all_responses['tasks'][0]['result'][0])
        # item_types = result.get('items_types', [])
        # count = result.get('se_results_count', 0)
        # organic_results = [item for item in items if item.get('type') == 'organic']
        # return SerpResult(
        #     keyword=result.get('keyword'),
        #     type=result.get('type'),
        #     se_domain=result.get('se_domain'),
        #     location_code=result.get('location_code'),
        #     language_code=result.get('language_code'),
        #     check_url=result.get('check_url'),
        #     datetime=result.get('datetime'),
        #     spell=result.get('spell'),
        #     refinement_chips=result.get('refinement_chips'),
        #     item_types=result.get('item_types'),
        #     se_results_count=result.get('se_results_count'),
        #     items_count=result.get('items_count'),
        #     items=result.get('items', [])
        # )
        return result
    except Exception as e:
        # Return an empty SerpResult or a SerpResult with error info
        raise e




@tool
def ask_user_input(prompt: str) -> str:
    """A tool that pauses the LLM to get user input.
    Args:
        prompt: The prompt to display to the user.
    """
    user_response = input(prompt).strip()
    return user_response

final_answer = FinalAnswerTool()

web_search = googleSearch
visit_web_page = VisitWebpageTool()
# keyword_research_tool = dataforseo_keyword_research
# If the agent does not answer, the model is overloaded, please use another model or the following Hugging Face Endpoint that also contains qwen2.5 coder:
# model_id='https://pflgm2locj2t89co.us-east-1.aws.endpoints.huggingface.cloud' 

# model = HfApiModel(
# max_tokens=2096,
# temperature=0.5,
# model_id='Qwen/Qwen2.5-Coder-32B-Instruct',# it is possible that this model may be overloaded
# custom_role_conversions=None,
# )
# model = LiteLLMModel(
# model_id="gpt-4.1",
# api_base="https://brizit.openai.azure.com",
# api_key="Qcb1IhcqMDU49gdtNUZKl9LF7c8vqPNDWjt9rePXNTei4ZquSJTVJQQJ99BDACYeBjFXJ3w3AAABACOGcxCm",
# max_tokens=2048,
# temperature=0.7,
# )


# model = AzureOpenAIServerModel(
#     model_id = "gpt-4o",
#     azure_endpoint="https://brizit.openai.azure.com",
#     api_key="Qcb1IhcqMDU49gdtNUZKl9LF7c8vqPNDWjt9rePXNTei4ZquSJTVJQQJ99BDACYeBjFXJ3w3AAABACOGcxCm",
#     api_version="2025-01-01-preview",
# )

model = LiteLLMModel(
    model_id="gemini/gemini-2.0-flash",
    api_key="AIzaSyB4LYkPHFhgc83B0ieV5mpSIBV5wFD-nhE",
    
)

# model = LiteLLMModel(
#     model_id="openai/gpt-4.1-nano",
#     api_key="********************************************************************************************************************************************************************",
    
# )
# Import tool from Hub
image_generation_tool = load_tool("agents-course/text-to-image", trust_remote_code=True)

with open("prompts.yaml", 'r') as stream:
    prompt_templates = yaml.safe_load(stream)
    
agent = CodeAgent(
    model=model,
    tools=[
        final_answer,
        web_search,
        get_current_time_in_timezone,
        visit_web_page,
        ask_user_input,
        # Direct DataForSEO tools that LLM can intelligently choose from
        serp_google_organic_live,
        labs_google_related_keywords,
        labs_google_keyword_ideas,
        backlinks_summary,
        keywords_google_ads_search_volume,
        labs_google_domain_rank_overview,
        labs_google_ranked_keywords,
        call_dataforseo,  # Flexible tool for any DataForSEO endpoint
        # Keep the old complex tools for now as backup
        smart_keyword_research_workflow,
        advanced_serp_based_clustering,
        comprehensive_website_analysis,
        autonomous_mcp_call
    ], ## add your tools here (don't remove final answer)
    max_steps=24,
    verbosity_level=1,
    grammar=None,
    planning_interval=3,
    name=None,
    description=None,
    prompt_templates=prompt_templates
)


# GradioUI(agent).launch()
# agent.run("Create for me a comprehensive, very detailed SEO plan for my new website: rcsplatform.com. ")


# # businessContextAgent = CodeAgent(model=model, tools=)
agent.run("Conduct a comprehensie keyword research for the site https://yaanidigital.com/")
          
          
"""and cluster them by intent and topical clustering. For content purposes, establish the pillar keywords and example content url. Understand that search engines care much more about deeper interpretation of user intent and thus salience is an important part of our analysis. Make sure all the keywords are relevant to the objective of the business. Output the final result in markdown table. Include all metrics like keyword difficulty, keyword volume CPC among others. First identify her competitors, identify keywords and content gap for a few keywords/pages that you will decide have highest ROI. Identify untapped low competition keywords that we can target. Give final report in markdown format")  # Run the agent"""
# agent.run("Which year did Jesus die")


agent.memory.get_full_steps()


"""
Notes: it should google the seed keywords 1 by 1 and use that to expand keywords from top search results.


first figure out what the business does - 1 shot llm call

Extract seed keywords - 1 shot llm call




"""