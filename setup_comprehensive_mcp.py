#!/usr/bin/env python3
"""
Setup script for the comprehensive DataForSEO MCP server
"""

import os
import subprocess
import sys
from pathlib import Path

def check_node_js():
    """Check if Node.js is installed"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js found: {result.stdout.strip()}")
            return True
        else:
            print("❌ Node.js not found")
            return False
    except FileNotFoundError:
        print("❌ Node.js not found")
        return False

def check_npm():
    """Check if npm is installed"""
    try:
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ npm found: {result.stdout.strip()}")
            return True
        else:
            print("❌ npm not found")
            return False
    except FileNotFoundError:
        print("❌ npm not found")
        return False

def clone_mcp_server():
    """Clone the comprehensive MCP server repository"""
    repo_url = "https://github.com/Skobyn/dataforseo-mcp-server.git"
    target_dir = "dataforseo-mcp-server"
    
    if os.path.exists(target_dir):
        print(f"✅ {target_dir} already exists")
        return True
    
    try:
        print(f"📥 Cloning {repo_url}...")
        result = subprocess.run(['git', 'clone', repo_url], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Successfully cloned to {target_dir}")
            return True
        else:
            print(f"❌ Failed to clone: {result.stderr}")
            return False
    except FileNotFoundError:
        print("❌ git not found. Please install git first.")
        return False

def install_dependencies():
    """Install npm dependencies for the MCP server"""
    server_dir = "dataforseo-mcp-server"
    
    if not os.path.exists(server_dir):
        print(f"❌ {server_dir} not found")
        return False
    
    try:
        print("📦 Installing npm dependencies...")
        result = subprocess.run(['npm', 'install'], cwd=server_dir, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Dependencies installed successfully")
            return True
        else:
            print(f"❌ Failed to install dependencies: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def build_server():
    """Build the TypeScript MCP server"""
    server_dir = "dataforseo-mcp-server"
    
    try:
        print("🔨 Building the MCP server...")
        result = subprocess.run(['npm', 'run', 'build'], cwd=server_dir, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Server built successfully")
            return True
        else:
            print(f"❌ Failed to build server: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error building server: {e}")
        return False

def check_credentials():
    """Check if DataForSEO credentials are set"""
    login = os.getenv('DATAFORSEO_LOGIN')
    password = os.getenv('DATAFORSEO_PASSWORD')
    
    if login and password:
        print("✅ DataForSEO credentials found in environment variables")
        return True
    else:
        print("⚠️  DataForSEO credentials not found")
        print("Please set the following environment variables:")
        print("export DATAFORSEO_LOGIN='your_login'")
        print("export DATAFORSEO_PASSWORD='your_password'")
        return False

def test_server():
    """Test if the MCP server can start"""
    server_dir = "dataforseo-mcp-server"
    server_path = os.path.join(server_dir, "dist", "index.js")
    
    if not os.path.exists(server_path):
        print(f"❌ Server executable not found at {server_path}")
        return False
    
    # Only test if credentials are available
    if not check_credentials():
        print("⚠️  Skipping server test due to missing credentials")
        return True
    
    try:
        print("🧪 Testing MCP server startup...")
        # Start server and immediately terminate to test if it can start
        process = subprocess.Popen(
            ['node', server_path],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            env={**os.environ, 'DATAFORSEO_LOGIN': os.getenv('DATAFORSEO_LOGIN'), 'DATAFORSEO_PASSWORD': os.getenv('DATAFORSEO_PASSWORD')}
        )
        
        # Wait a moment then terminate
        import time
        time.sleep(3)
        process.terminate()
        process.wait()
        
        print("✅ Server test completed")
        return True
        
    except Exception as e:
        print(f"❌ Server test failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 Setting up Comprehensive DataForSEO MCP Server")
    print("=" * 50)
    
    # Check prerequisites
    if not check_node_js():
        print("\n📋 To install Node.js:")
        print("macOS: brew install node")
        print("Ubuntu: sudo apt install nodejs npm")
        print("Windows: Download from https://nodejs.org/")
        return False
    
    if not check_npm():
        print("\n📋 npm should come with Node.js. Please reinstall Node.js.")
        return False
    
    # Clone and setup
    if not clone_mcp_server():
        return False
    
    if not install_dependencies():
        return False
    
    if not build_server():
        return False
    
    # Test
    test_server()
    
    print("\n🎉 Setup Complete!")
    print("=" * 50)
    print("✅ Comprehensive DataForSEO MCP Server is ready to use")
    print("\n📋 Next steps:")
    print("1. Set your DataForSEO credentials:")
    print("   export DATAFORSEO_LOGIN='your_login'")
    print("   export DATAFORSEO_PASSWORD='your_password'")
    print("2. The server will be automatically started when you use the tools")
    print("3. Available tools include:")
    print("   - serp_google_organic_live")
    print("   - labs_google_related_keywords")
    print("   - labs_google_keyword_ideas")
    print("   - backlinks_summary")
    print("   - keywords_google_ads_search_volume")
    print("   - And many more!")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
