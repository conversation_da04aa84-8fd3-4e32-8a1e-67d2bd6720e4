#!/usr/bin/env python3
"""
Test the flexible DataForSEO approach
Demonstrates how the LLM can call ANY DataForSEO endpoint dynamically
"""

import os
import sys
sys.path.append('.')

from tools.direct_dataforseo_client import call_dataforseo

def test_flexible_dataforseo():
    """Test the flexible DataForSEO API caller"""
    
    print("🧪 Testing Flexible DataForSEO API Caller")
    print("=" * 60)
    
    # Check credentials
    username = os.getenv('DATAFORSEO_USERNAME') or os.getenv('DATAFORSEO_LOGIN')
    password = os.getenv('DATAFORSEO_PASSWORD')
    
    if not username or not password:
        print("⚠️  DataForSEO credentials not found!")
        print("Please set DATAFORSEO_USERNAME and DATAFORSEO_PASSWORD in .env file")
        print("For now, testing will show connection errors\n")
    else:
        print(f"✅ Credentials found for user: {username[:3]}...{username[-3:]}")
    
    # Test 1: SERP Analysis (same as specific function)
    print("\n1. Testing SERP Analysis via flexible caller:")
    print("-" * 50)
    try:
        result = call_dataforseo(
            endpoint='/serp/google/organic/live/advanced',
            keyword='python programming',
            location_code=2840,
            language_code='en',
            device='desktop'
        )
        if 'error' in result:
            print(f"❌ SERP Error: {result['error']}")
        else:
            print(f"✅ SERP Success: {result.get('status_code', 'unknown')} status")
            if 'tasks' in result and result['tasks']:
                task = result['tasks'][0]
                if 'result' in task and task['result']:
                    items = task['result'][0].get('items', [])
                    organic_count = len([item for item in items if item.get('type') == 'organic'])
                    print(f"   Found {organic_count} organic results")
    except Exception as e:
        print(f"❌ SERP Exception: {e}")
    
    # Test 2: Keyword Difficulty (not available as specific function)
    print("\n2. Testing Keyword Difficulty via flexible caller:")
    print("-" * 50)
    try:
        result = call_dataforseo(
            endpoint='/dataforseo_labs/google/bulk_keyword_difficulty/live',
            keywords=['seo tools', 'keyword research', 'digital marketing'],
            location_code=2840,
            language_code='en'
        )
        if 'error' in result:
            print(f"❌ Keyword Difficulty Error: {result['error']}")
        else:
            print(f"✅ Keyword Difficulty Success: {result.get('status_code', 'unknown')} status")
            if 'tasks' in result and result['tasks']:
                task = result['tasks'][0]
                if 'result' in task and task['result']:
                    keywords = task['result']
                    print(f"   Found difficulty data for {len(keywords)} keywords")
                    for kw in keywords[:3]:
                        keyword = kw.get('keyword', 'N/A')
                        difficulty = kw.get('keyword_difficulty', 'N/A')
                        print(f"   {keyword}: difficulty {difficulty}")
    except Exception as e:
        print(f"❌ Keyword Difficulty Exception: {e}")
    
    # Test 3: On-Page Analysis (not available as specific function)
    print("\n3. Testing On-Page Analysis via flexible caller:")
    print("-" * 50)
    try:
        result = call_dataforseo(
            endpoint='/on_page/summary/live',
            target='dataforseo.com'
        )
        if 'error' in result:
            print(f"❌ On-Page Error: {result['error']}")
        else:
            print(f"✅ On-Page Success: {result.get('status_code', 'unknown')} status")
            if 'tasks' in result and result['tasks']:
                task = result['tasks'][0]
                if 'result' in task and task['result']:
                    summary = task['result'][0]
                    crawled_pages = summary.get('crawled_pages', 'N/A')
                    print(f"   Crawled pages: {crawled_pages}")
    except Exception as e:
        print(f"❌ On-Page Exception: {e}")

def demonstrate_llm_flexibility():
    """Demonstrate how the LLM can use the flexible approach"""
    
    print("\n🤖 LLM Flexibility Demonstration")
    print("=" * 60)
    
    scenarios = [
        {
            "user_query": "Check keyword difficulty for 'content marketing', 'seo tools', 'digital marketing'",
            "llm_reasoning": "User wants keyword difficulty. I'll use the flexible caller:",
            "llm_call": """call_dataforseo(
    endpoint='/dataforseo_labs/google/bulk_keyword_difficulty/live',
    keywords=['content marketing', 'seo tools', 'digital marketing'],
    location_code=2840,
    language_code='en'
)"""
        },
        {
            "user_query": "Analyze the on-page SEO of example.com",
            "llm_reasoning": "User wants on-page analysis. I'll use the flexible caller:",
            "llm_call": """call_dataforseo(
    endpoint='/on_page/summary/live',
    target='example.com'
)"""
        },
        {
            "user_query": "Get Google Maps results for 'restaurants near me' in New York",
            "llm_reasoning": "User wants Google Maps data. I'll use the flexible caller:",
            "llm_call": """call_dataforseo(
    endpoint='/serp/google/maps/live/advanced',
    keyword='restaurants near me',
    location_code=1023191,  # New York
    language_code='en'
)"""
        },
        {
            "user_query": "Find trending keywords in the 'technology' category",
            "llm_reasoning": "User wants trending keywords. I'll use the flexible caller:",
            "llm_call": """call_dataforseo(
    endpoint='/dataforseo_labs/google/categories_for_domain/live',
    target='tech.com',
    location_code=2840
)"""
        },
        {
            "user_query": "Get domain analytics overview for semrush.com",
            "llm_reasoning": "User wants domain analytics. I'll use the flexible caller:",
            "llm_call": """call_dataforseo(
    endpoint='/domain_analytics/google/overview/live',
    target='semrush.com',
    location_code=2840,
    language_code='en'
)"""
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{i}. User Query: '{scenario['user_query']}'")
        print(f"   LLM Reasoning: {scenario['llm_reasoning']}")
        print(f"   LLM Call:")
        print(f"   {scenario['llm_call']}")

def show_available_endpoints():
    """Show some of the available DataForSEO endpoints"""
    
    print("\n📚 Available DataForSEO Endpoints (100+ total)")
    print("=" * 60)
    
    endpoints = {
        "SERP APIs": [
            "/serp/google/organic/live/advanced",
            "/serp/google/maps/live/advanced", 
            "/serp/google/images/live/advanced",
            "/serp/google/news/live/advanced",
            "/serp/bing/organic/live/advanced",
            "/serp/youtube/organic/live/advanced"
        ],
        "DataForSEO Labs": [
            "/dataforseo_labs/google/related_keywords/live",
            "/dataforseo_labs/google/keyword_ideas/live",
            "/dataforseo_labs/google/bulk_keyword_difficulty/live",
            "/dataforseo_labs/google/ranked_keywords/live",
            "/dataforseo_labs/google/domain_rank_overview/live",
            "/dataforseo_labs/google/categories_for_domain/live"
        ],
        "Keywords Data": [
            "/keywords_data/google_ads/search_volume/live",
            "/keywords_data/google_ads/keywords_for_keywords/live",
            "/keywords_data/bing/search_volume/live"
        ],
        "On-Page": [
            "/on_page/summary/live",
            "/on_page/pages/live",
            "/on_page/lighthouse/live",
            "/on_page/page_screenshot/live"
        ],
        "Backlinks": [
            "/backlinks/summary/live",
            "/backlinks/history/live",
            "/backlinks/anchors/live",
            "/backlinks/referring_domains/live"
        ],
        "Domain Analytics": [
            "/domain_analytics/google/overview/live",
            "/domain_analytics/google/technologies/live",
            "/domain_analytics/google/whois/live"
        ]
    }
    
    for category, endpoint_list in endpoints.items():
        print(f"\n{category}:")
        for endpoint in endpoint_list:
            print(f"  • {endpoint}")

if __name__ == "__main__":
    test_flexible_dataforseo()
    demonstrate_llm_flexibility()
    show_available_endpoints()
    
    print("\n" + "=" * 60)
    print("🎯 Benefits of the Flexible Approach:")
    print("✅ LLM can access ALL 100+ DataForSEO endpoints")
    print("✅ No need to pre-define every possible function")
    print("✅ LLM intelligently maps user requests to API calls")
    print("✅ Easy to use new endpoints as DataForSEO adds them")
    print("✅ Maximum flexibility with minimal code")
    print("✅ LLM can combine multiple endpoints creatively")
    print("\n💡 The LLM becomes the intelligent API router!")
    print("=" * 60)
