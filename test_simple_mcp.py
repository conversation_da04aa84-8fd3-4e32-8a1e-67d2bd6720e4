#!/usr/bin/env python3
"""
Test the simplified MCP approach
"""

import os
import sys
sys.path.append('.')

from tools.autonomous_mcp_agent import (
    dataforseo_serp_analysis,
    dataforseo_keyword_research,
    dataforseo_competitor_keywords
)

def test_simple_mcp_tools():
    """Test the simplified MCP tools"""
    
    print("🧪 Testing Simplified MCP Tools")
    print("=" * 50)
    
    # Test 1: SERP Analysis
    print("\n1. Testing SERP Analysis:")
    print("-" * 30)
    try:
        result = dataforseo_serp_analysis("python programming")
        print(f"✅ SERP Analysis Result: {result}")
    except Exception as e:
        print(f"❌ SERP Analysis Error: {e}")
    
    # Test 2: Keyword Research
    print("\n2. Testing Keyword Research:")
    print("-" * 30)
    try:
        result = dataforseo_keyword_research(["seo tools", "keyword research"])
        print(f"✅ Keyword Research Result: {result}")
    except Exception as e:
        print(f"❌ Keyword Research Error: {e}")
    
    # Test 3: Competitor Keywords
    print("\n3. Testing Competitor Keywords:")
    print("-" * 30)
    try:
        result = dataforseo_competitor_keywords("semrush.com")
        print(f"✅ Competitor Keywords Result: {result}")
    except Exception as e:
        print(f"❌ Competitor Keywords Error: {e}")

def demonstrate_llm_approach():
    """Demonstrate how the LLM would choose tools"""
    
    print("\n🤖 LLM Tool Selection Demo")
    print("=" * 50)
    
    # Simulate different user queries and show which tool the LLM should choose
    queries = [
        {
            "query": "What are the search results for 'digital marketing'?",
            "expected_tool": "dataforseo_serp_analysis",
            "reason": "User wants SERP data"
        },
        {
            "query": "Get keyword metrics for 'content marketing' and 'social media'",
            "expected_tool": "dataforseo_keyword_research", 
            "reason": "User wants keyword metrics"
        },
        {
            "query": "What keywords does ahrefs.com rank for?",
            "expected_tool": "dataforseo_competitor_keywords",
            "reason": "User wants competitor keyword analysis"
        },
        {
            "query": "Analyze the SEO of https://example.com",
            "expected_tool": "dataforseo_onpage_analysis",
            "reason": "User wants on-page SEO analysis"
        }
    ]
    
    for i, item in enumerate(queries, 1):
        print(f"\n{i}. Query: '{item['query']}'")
        print(f"   → Expected Tool: {item['expected_tool']}")
        print(f"   → Reason: {item['reason']}")

if __name__ == "__main__":
    # Set up environment variables for testing (you'll need to add your credentials)
    if not os.getenv('DATAFORSEO_USERNAME'):
        print("⚠️  DataForSEO credentials not found in environment variables")
        print("   Set DATAFORSEO_USERNAME and DATAFORSEO_PASSWORD to test with real API")
        print("   For now, testing will show error responses\n")
    
    test_simple_mcp_tools()
    demonstrate_llm_approach()
    
    print("\n" + "=" * 50)
    print("🎯 Key Benefits of This Approach:")
    print("✅ No complex rule-based routing logic")
    print("✅ LLM naturally understands user intent")
    print("✅ Each tool has a clear, single purpose")
    print("✅ Easy to add new MCP tools")
    print("✅ Leverages LLM's reasoning capabilities")
    print("=" * 50)
