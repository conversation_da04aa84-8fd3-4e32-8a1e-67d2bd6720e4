#!/usr/bin/env python3
"""
Test the direct DataForSEO client
"""

import os
import sys
sys.path.append('.')

from tools.direct_dataforseo_client import (
    serp_google_organic_live,
    labs_google_related_keywords,
    labs_google_keyword_ideas,
    backlinks_summary,
    keywords_google_ads_search_volume,
    labs_google_domain_rank_overview,
    labs_google_ranked_keywords
)

def test_direct_dataforseo_tools():
    """Test the direct DataForSEO tools"""
    
    print("🧪 Testing Direct DataForSEO API Tools")
    print("=" * 60)
    
    # Check credentials
    username = os.getenv('DATAFORSEO_USERNAME') or os.getenv('DATAFORSEO_LOGIN')
    password = os.getenv('DATAFORSEO_PASSWORD')
    
    if not username or not password:
        print("⚠️  DataForSEO credentials not found!")
        print("Please set DATAFORSEO_USERNAME and DATAFORSEO_PASSWORD in .env file")
        print("For now, testing will show connection errors\n")
    else:
        print(f"✅ Credentials found for user: {username[:3]}...{username[-3:]}")
    
    # Test 1: Google Organic SERP
    print("\n1. Testing Google Organic SERP:")
    print("-" * 40)
    try:
        result = serp_google_organic_live("python programming")
        if 'error' in result:
            print(f"❌ SERP Error: {result['error']}")
        else:
            print(f"✅ SERP Success: {result.get('status_code', 'unknown')} status")
            if 'tasks' in result and result['tasks']:
                task = result['tasks'][0]
                if 'result' in task and task['result']:
                    items = task['result'][0].get('items', [])
                    organic_count = len([item for item in items if item.get('type') == 'organic'])
                    print(f"   Found {organic_count} organic results")
                    if organic_count > 0:
                        print(f"   First result: {items[0].get('title', 'N/A')}")
    except Exception as e:
        print(f"❌ SERP Exception: {e}")
    
    # Test 2: Related Keywords
    print("\n2. Testing Related Keywords:")
    print("-" * 40)
    try:
        result = labs_google_related_keywords("seo tools", limit=5)
        if 'error' in result:
            print(f"❌ Related Keywords Error: {result['error']}")
        else:
            print(f"✅ Related Keywords Success: {result.get('status_code', 'unknown')} status")
            if 'tasks' in result and result['tasks']:
                task = result['tasks'][0]
                if 'result' in task and task['result']:
                    keywords = task['result']
                    print(f"   Found {len(keywords)} related keywords")
                    for i, kw in enumerate(keywords[:3]):
                        keyword = kw.get('keyword', 'N/A')
                        search_volume = kw.get('search_volume', 'N/A')
                        print(f"   {i+1}. {keyword} (Volume: {search_volume})")
    except Exception as e:
        print(f"❌ Related Keywords Exception: {e}")
    
    # Test 3: Search Volume
    print("\n3. Testing Search Volume:")
    print("-" * 40)
    try:
        result = keywords_google_ads_search_volume(["seo", "keyword research"])
        if 'error' in result:
            print(f"❌ Search Volume Error: {result['error']}")
        else:
            print(f"✅ Search Volume Success: {result.get('status_code', 'unknown')} status")
            if 'tasks' in result and result['tasks']:
                task = result['tasks'][0]
                if 'result' in task and task['result']:
                    keywords = task['result']
                    print(f"   Found volume data for {len(keywords)} keywords")
                    for kw_data in keywords[:2]:
                        keyword = kw_data.get('keyword', 'N/A')
                        search_volume = kw_data.get('search_volume', 'N/A')
                        print(f"   {keyword}: {search_volume}")
    except Exception as e:
        print(f"❌ Search Volume Exception: {e}")

def demonstrate_llm_workflow():
    """Demonstrate how the LLM would use these tools in a workflow"""
    
    print("\n🤖 LLM Workflow Demonstration")
    print("=" * 60)
    
    workflows = [
        {
            "user_query": "I want to do keyword research for 'content marketing'",
            "llm_reasoning": "User wants keyword research. I should:",
            "steps": [
                "1. Get related keywords: labs_google_related_keywords('content marketing')",
                "2. Expand with keyword ideas: labs_google_keyword_ideas('content marketing')", 
                "3. Get search volume: keywords_google_ads_search_volume([keywords])",
                "4. Analyze SERP competition: serp_google_organic_live('content marketing')"
            ]
        },
        {
            "user_query": "Analyze the SEO competition for 'digital marketing tools'",
            "llm_reasoning": "User wants competitive analysis. I should:",
            "steps": [
                "1. Get SERP results: serp_google_organic_live('digital marketing tools')",
                "2. Analyze top competitors: labs_google_ranked_keywords(competitor_domain)",
                "3. Get domain metrics: labs_google_domain_rank_overview(competitor_domain)",
                "4. Check backlinks: backlinks_summary(competitor_domain)"
            ]
        },
        {
            "user_query": "What keywords does semrush.com rank for?",
            "llm_reasoning": "User wants competitor keyword analysis. I should:",
            "steps": [
                "1. Get ranked keywords: labs_google_ranked_keywords('semrush.com')",
                "2. Get domain overview: labs_google_domain_rank_overview('semrush.com')",
                "3. Analyze top keywords for opportunities"
            ]
        }
    ]
    
    for i, workflow in enumerate(workflows, 1):
        print(f"\n{i}. User Query: '{workflow['user_query']}'")
        print(f"   LLM Reasoning: {workflow['llm_reasoning']}")
        for step in workflow['steps']:
            print(f"   {step}")

if __name__ == "__main__":
    test_direct_dataforseo_tools()
    demonstrate_llm_workflow()
    
    print("\n" + "=" * 60)
    print("🎯 Benefits of the Direct API Approach:")
    print("✅ No MCP complexity - direct API calls")
    print("✅ LLM intelligently chooses the right tools")
    print("✅ Real DataForSEO data with full API coverage")
    print("✅ Simple, reliable, and fast")
    print("✅ Easy to debug and extend")
    print("✅ Production-ready with proper error handling")
    print("\n📋 Setup Instructions:")
    print("1. Set credentials in .env file:")
    print("   DATAFORSEO_USERNAME=your_username")
    print("   DATAFORSEO_PASSWORD=your_password")
    print("2. The tools are ready to use in your agent!")
    print("=" * 60)
