from typing import Dict, List
from serp_models import SerpItem, SerpResult
from serp_parser import parse_serp_result


class SerpAnalyzer:
    """Helper class for analyzing SERP results"""
    
    def __init__(self, serp_result: SerpResult):
        self.result = serp_result
    
    def get_organic_results(self) -> List[SerpItem]:
        """Get only organic search results"""
        return [item for item in self.result.items if item.type == 'organic']
    
    def get_related_searches(self) -> List[str]:
        """Get related search suggestions"""
        for item in self.result.items:
            if item.type == 'related_searches' and item.items:
                return item.items
        return []
    
    def get_top_domains(self, limit: int = 10) -> List[str]:
        """Get top ranking domains"""
        organic_results = self.get_organic_results()
        return [item.domain for item in organic_results[:limit] if item.domain]
    
    def get_featured_snippets(self) -> List[SerpItem]:
        """Get featured snippet results"""
        return [item for item in self.get_organic_results() 
                if item.is_featured_snippet]
    
    def extract_competitor_analysis(self) -> Dict:
        """Extract data useful for competitor analysis"""
        organic_results = self.get_organic_results()
        
        return {
            'total_results': self.result.se_results_count,
            'top_10_domains': [item.domain for item in organic_results[:10]],
            'featured_snippets': len(self.get_featured_snippets()),
            'related_keywords': self.get_related_searches(),
            'serp_features': self.result.item_types
        }

# Usage example:
def process_serp_response(response_data: Dict) -> SerpAnalyzer:
    """Process full DataForSEO response and return analyzer"""
    # Extract the result from the full response structure
    task_result = response_data['tasks'][0]['result'][0]
    
    # Parse into structured format
    serp_result = parse_serp_result(task_result)
    
    # Return analyzer for easy data extraction
    return SerpAnalyzer(serp_result)