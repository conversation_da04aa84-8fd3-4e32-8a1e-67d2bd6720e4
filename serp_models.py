from dataclasses import dataclass
from typing import List, Optional, Any, Dict
from datetime import datetime

@dataclass
class SerpLink:
    """Represents a link element in SERP results"""
    type: str
    title: str
    description: Optional[str]
    url: str
    domain: str

@dataclass
class SerpItem:
    """Represents an individual SERP item (organic result, related search, etc.)"""
    type: str
    rank_group: Optional[int] = None
    rank_absolute: Optional[int] = None
    position: Optional[str] = None
    xpath: Optional[str] = None
    domain: Optional[str] = None
    title: Optional[str] = None
    url: Optional[str] = None
    cache_url: Optional[str] = None
    related_search_url: Optional[str] = None
    breadcrumb: Optional[str] = None
    website_name: Optional[str] = None
    is_image: Optional[bool] = None
    is_video: Optional[bool] = None
    is_featured_snippet: Optional[bool] = None
    is_malicious: Optional[bool] = None
    is_web_story: Optional[bool] = None
    description: Optional[str] = None
    pre_snippet: Optional[str] = None
    extended_snippet: Optional[str] = None
    images: Optional[List[Any]] = None
    amp_version: Optional[bool] = None
    rating: Optional[float] = None
    price: Optional[Dict] = None
    highlighted: Optional[List[str]] = None
    links: Optional[List[SerpLink]] = None
    faq: Optional[List[Any]] = None
    extended_people_also_search: Optional[List[Any]] = None
    about_this_result: Optional[Dict] = None
    related_result: Optional[Dict] = None
    timestamp: Optional[str] = None
    rectangle: Optional[Dict] = None
    items: Optional[List[str]] = None  # For related_searches type

@dataclass
class SerpResult:
    """Main SERP result structure"""
    keyword: str
    type: str
    se_domain: str
    location_code: int
    language_code: str
    check_url: str
    datetime: str
    spell: Optional[Any]
    refinement_chips: Optional[List[Any]]
    item_types: List[str]
    se_results_count: int
    items_count: int
    items: List[SerpItem]