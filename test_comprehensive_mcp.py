#!/usr/bin/env python3
"""
Test the comprehensive MCP approach
"""

import os
import sys
sys.path.append('.')

from tools.comprehensive_mcp_client import (
    serp_google_organic_live,
    labs_google_related_keywords,
    labs_google_keyword_ideas,
    backlinks_summary,
    keywords_google_ads_search_volume
)

def test_comprehensive_mcp_tools():
    """Test the comprehensive MCP tools"""
    
    print("🧪 Testing Comprehensive DataForSEO MCP Tools")
    print("=" * 60)
    
    # Check credentials
    if not os.getenv('DATAFORSEO_LOGIN') or not os.getenv('DATAFORSEO_PASSWORD'):
        print("⚠️  DataForSEO credentials not found!")
        print("Please set DATAFORSEO_LOGIN and DATAFORSEO_PASSWORD environment variables")
        print("For now, testing will show connection errors\n")
    
    # Test 1: Google Organic SERP
    print("\n1. Testing Google Organic SERP:")
    print("-" * 40)
    try:
        result = serp_google_organic_live("python programming")
        print(f"✅ SERP Result: {result.get('status', 'unknown')}")
        if 'tasks' in result and result['tasks']:
            task_result = result['tasks'][0].get('result', [])
            if task_result:
                organic_count = len([item for item in task_result[0].get('items', []) if item.get('type') == 'organic'])
                print(f"   Found {organic_count} organic results")
        else:
            print(f"   Response: {result}")
    except Exception as e:
        print(f"❌ SERP Error: {e}")
    
    # Test 2: Related Keywords
    print("\n2. Testing Related Keywords:")
    print("-" * 40)
    try:
        result = labs_google_related_keywords("seo tools", limit=5)
        print(f"✅ Related Keywords Result: {result.get('status', 'unknown')}")
        if 'tasks' in result and result['tasks']:
            task_result = result['tasks'][0].get('result', [])
            if task_result:
                keyword_count = len(task_result)
                print(f"   Found {keyword_count} related keywords")
                # Show first few keywords
                for i, kw in enumerate(task_result[:3]):
                    keyword = kw.get('keyword', 'N/A')
                    search_volume = kw.get('search_volume', 'N/A')
                    print(f"   {i+1}. {keyword} (Volume: {search_volume})")
        else:
            print(f"   Response: {result}")
    except Exception as e:
        print(f"❌ Related Keywords Error: {e}")
    
    # Test 3: Keyword Ideas
    print("\n3. Testing Keyword Ideas:")
    print("-" * 40)
    try:
        result = labs_google_keyword_ideas("digital marketing", limit=10)
        print(f"✅ Keyword Ideas Result: {result.get('status', 'unknown')}")
        if 'tasks' in result and result['tasks']:
            task_result = result['tasks'][0].get('result', [])
            if task_result:
                idea_count = len(task_result)
                print(f"   Found {idea_count} keyword ideas")
        else:
            print(f"   Response: {result}")
    except Exception as e:
        print(f"❌ Keyword Ideas Error: {e}")
    
    # Test 4: Backlinks Summary
    print("\n4. Testing Backlinks Summary:")
    print("-" * 40)
    try:
        result = backlinks_summary("dataforseo.com", limit=5)
        print(f"✅ Backlinks Result: {result.get('status', 'unknown')}")
        if 'tasks' in result and result['tasks']:
            task_result = result['tasks'][0].get('result', [])
            if task_result:
                backlink_count = task_result[0].get('backlinks', 0)
                referring_domains = task_result[0].get('referring_domains', 0)
                print(f"   Backlinks: {backlink_count}, Referring Domains: {referring_domains}")
        else:
            print(f"   Response: {result}")
    except Exception as e:
        print(f"❌ Backlinks Error: {e}")
    
    # Test 5: Search Volume
    print("\n5. Testing Search Volume:")
    print("-" * 40)
    try:
        result = keywords_google_ads_search_volume(["seo", "keyword research"])
        print(f"✅ Search Volume Result: {result.get('status', 'unknown')}")
        if 'tasks' in result and result['tasks']:
            task_result = result['tasks'][0].get('result', [])
            if task_result:
                print(f"   Found volume data for {len(task_result)} keywords")
                for kw_data in task_result[:2]:
                    keyword = kw_data.get('keyword', 'N/A')
                    search_volume = kw_data.get('search_volume', 'N/A')
                    print(f"   {keyword}: {search_volume}")
        else:
            print(f"   Response: {result}")
    except Exception as e:
        print(f"❌ Search Volume Error: {e}")

def demonstrate_llm_workflow():
    """Demonstrate how the LLM would use these tools in a workflow"""
    
    print("\n🤖 LLM Workflow Demonstration")
    print("=" * 60)
    
    workflows = [
        {
            "user_query": "I want to do keyword research for 'content marketing'",
            "llm_reasoning": "User wants keyword research. I should:",
            "steps": [
                "1. Get related keywords using labs_google_related_keywords",
                "2. Expand with keyword ideas using labs_google_keyword_ideas", 
                "3. Get search volume data using keywords_google_ads_search_volume",
                "4. Analyze SERP competition using serp_google_organic_live"
            ]
        },
        {
            "user_query": "Analyze the SEO competition for 'digital marketing tools'",
            "llm_reasoning": "User wants competitive analysis. I should:",
            "steps": [
                "1. Get SERP results using serp_google_organic_live",
                "2. Analyze top competitors' backlinks using backlinks_summary",
                "3. Find related keywords competitors might rank for",
                "4. Provide competitive insights and opportunities"
            ]
        },
        {
            "user_query": "Find keyword opportunities for my SaaS website",
            "llm_reasoning": "User wants keyword opportunities. I should:",
            "steps": [
                "1. Get keyword ideas for their industry using labs_google_keyword_ideas",
                "2. Check search volume and difficulty",
                "3. Analyze SERP features and competition",
                "4. Suggest low-competition, high-value keywords"
            ]
        }
    ]
    
    for i, workflow in enumerate(workflows, 1):
        print(f"\n{i}. User Query: '{workflow['user_query']}'")
        print(f"   LLM Reasoning: {workflow['llm_reasoning']}")
        for step in workflow['steps']:
            print(f"   {step}")

if __name__ == "__main__":
    test_comprehensive_mcp_tools()
    demonstrate_llm_workflow()
    
    print("\n" + "=" * 60)
    print("🎯 Benefits of the Comprehensive MCP Approach:")
    print("✅ Access to 100+ DataForSEO API endpoints")
    print("✅ Real-time data from Google, Bing, and other sources")
    print("✅ LLM intelligently chooses the right tools")
    print("✅ No complex routing logic needed")
    print("✅ Extensible - easy to add more tools")
    print("✅ Production-ready with proper error handling")
    print("\n📋 Setup Instructions:")
    print("1. Run: python setup_comprehensive_mcp.py")
    print("2. Set credentials: export DATAFORSEO_LOGIN='your_login'")
    print("3. Set credentials: export DATAFORSEO_PASSWORD='your_password'")
    print("4. The tools will automatically start the MCP server when needed")
    print("=" * 60)
