#!/usr/bin/env node

/**
 * Simple DataForSEO MCP Server
 * A working MCP server for DataForSEO API using the latest MCP SDK
 */

const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const {
  StdioServerTransport,
} = require('@modelcontextprotocol/sdk/server/stdio.js');
const axios = require('axios');

// Get credentials from environment (support both LOGIN and USERNAME formats)
const login = process.env.DATAFORSEO_LOGIN || process.env.DATAFORSEO_USERNAME;
const password = process.env.DATAFORSEO_PASSWORD;

if (!login || !password) {
  console.error('Error: DataForSEO credentials are required');
  console.error(
    'Set either DATAFORSEO_LOGIN/DATAFORSEO_PASSWORD or DATAFORSEO_USERNAME/DATAFORSEO_PASSWORD',
  );
  process.exit(1);
}

// Create axios client for DataForSEO API
const apiClient = axios.create({
  baseURL: 'https://api.dataforseo.com/v3',
  auth: {
    username: login,
    password: password,
  },
  headers: {
    'Content-Type': 'application/json',
  },
});

// Create MCP server
const server = new Server(
  {
    name: 'simple-dataforseo-mcp-server',
    version: '1.0.0',
  },
  {
    capabilities: {
      tools: {},
    },
  },
);

// Tool: Google Organic SERP Live
server.setRequestHandler({ method: 'tools/call' }, async (request) => {
  const { name, arguments: args } = request.params;

  try {
    let result;

    switch (name) {
      case 'serp_google_organic_live':
        result = await apiClient.post('/serp/google/organic/live/advanced', [
          {
            keyword: args.keyword,
            location_code: args.location_code || 2840,
            language_code: args.language_code || 'en',
            device: args.device || 'desktop',
            os: args.os || 'windows',
          },
        ]);
        break;

      case 'labs_google_related_keywords':
        result = await apiClient.post(
          '/dataforseo_labs/google/related_keywords/live',
          [
            {
              keyword: args.keyword,
              location_code: args.location_code || 2840,
              language_code: args.language_code || 'en',
              limit: args.limit || 10,
            },
          ],
        );
        break;

      case 'labs_google_keyword_ideas':
        result = await apiClient.post(
          '/dataforseo_labs/google/keyword_ideas/live',
          [
            {
              keyword: args.keyword,
              location_code: args.location_code || 2840,
              language_code: args.language_code || 'en',
              limit: args.limit || 100,
            },
          ],
        );
        break;

      case 'backlinks_summary':
        result = await apiClient.post('/backlinks/summary/live', [
          {
            target: args.target,
            limit: args.limit || 100,
          },
        ]);
        break;

      case 'keywords_google_ads_search_volume':
        result = await apiClient.post(
          '/keywords_data/google_ads/search_volume/live',
          [
            {
              keywords: args.keywords,
              location_code: args.location_code || 2840,
              language_code: args.language_code || 'en',
            },
          ],
        );
        break;

      default:
        throw new Error(`Unknown tool: ${name}`);
    }

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(result.data, null, 2),
        },
      ],
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(
            {
              error: error.message,
              details: error.response?.data || 'No additional details',
            },
            null,
            2,
          ),
        },
      ],
      isError: true,
    };
  }
});

// List available tools
server.setRequestHandler({ method: 'tools/list' }, async () => {
  return {
    tools: [
      {
        name: 'serp_google_organic_live',
        description: 'Get live Google organic search results',
        inputSchema: {
          type: 'object',
          properties: {
            keyword: { type: 'string', description: 'Search keyword' },
            location_code: {
              type: 'number',
              description: 'Location code (default: 2840 for USA)',
            },
            language_code: {
              type: 'string',
              description: 'Language code (default: en)',
            },
            device: {
              type: 'string',
              description: 'Device type (default: desktop)',
            },
            os: {
              type: 'string',
              description: 'Operating system (default: windows)',
            },
          },
          required: ['keyword'],
        },
      },
      {
        name: 'labs_google_related_keywords',
        description: 'Get related keywords with SEO metrics',
        inputSchema: {
          type: 'object',
          properties: {
            keyword: { type: 'string', description: 'Seed keyword' },
            location_code: {
              type: 'number',
              description: 'Location code (default: 2840 for USA)',
            },
            language_code: {
              type: 'string',
              description: 'Language code (default: en)',
            },
            limit: {
              type: 'number',
              description: 'Number of results (default: 10)',
            },
          },
          required: ['keyword'],
        },
      },
      {
        name: 'labs_google_keyword_ideas',
        description: 'Get keyword ideas for comprehensive research',
        inputSchema: {
          type: 'object',
          properties: {
            keyword: { type: 'string', description: 'Seed keyword' },
            location_code: {
              type: 'number',
              description: 'Location code (default: 2840 for USA)',
            },
            language_code: {
              type: 'string',
              description: 'Language code (default: en)',
            },
            limit: {
              type: 'number',
              description: 'Number of results (default: 100)',
            },
          },
          required: ['keyword'],
        },
      },
      {
        name: 'backlinks_summary',
        description: 'Get backlinks summary for a domain',
        inputSchema: {
          type: 'object',
          properties: {
            target: { type: 'string', description: 'Target domain to analyze' },
            limit: {
              type: 'number',
              description: 'Number of backlinks (default: 100)',
            },
          },
          required: ['target'],
        },
      },
      {
        name: 'keywords_google_ads_search_volume',
        description: 'Get search volume data for keywords',
        inputSchema: {
          type: 'object',
          properties: {
            keywords: {
              type: 'array',
              items: { type: 'string' },
              description: 'List of keywords',
            },
            location_code: {
              type: 'number',
              description: 'Location code (default: 2840 for USA)',
            },
            language_code: {
              type: 'string',
              description: 'Language code (default: en)',
            },
          },
          required: ['keywords'],
        },
      },
    ],
  };
});

// Start the server
async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.error('Simple DataForSEO MCP Server started');
}

main().catch((error) => {
  console.error('Server error:', error);
  process.exit(1);
});
