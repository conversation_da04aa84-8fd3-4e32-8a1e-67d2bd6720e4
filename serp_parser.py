from typing import Dict
from serp_models import <PERSON>p<PERSON><PERSON>, SerpLink, SerpResult

def parse_serp_result(raw_result: Dict) -> SerpResult:
    """Parse raw DataForSEO SERP result into structured objects"""
    
    # Parse items
    items = []
    for item_data in raw_result.get('items', []):
        # Parse links if they exist
        links = None
        if item_data.get('links'):
            links = [
                SerpLink(
                    type=link.get('type'),
                    title=link.get('title'),
                    description=link.get('description'),
                    url=link.get('url'),
                    domain=link.get('domain')
                )
                for link in item_data['links']
            ]
        
        item = SerpItem(
            type=item_data.get('type'),
            rank_group=item_data.get('rank_group'),
            rank_absolute=item_data.get('rank_absolute'),
            position=item_data.get('position'),
            xpath=item_data.get('xpath'),
            domain=item_data.get('domain'),
            title=item_data.get('title'),
            url=item_data.get('url'),
            cache_url=item_data.get('cache_url'),
            related_search_url=item_data.get('related_search_url'),
            breadcrumb=item_data.get('breadcrumb'),
            website_name=item_data.get('website_name'),
            is_image=item_data.get('is_image'),
            is_video=item_data.get('is_video'),
            is_featured_snippet=item_data.get('is_featured_snippet'),
            is_malicious=item_data.get('is_malicious'),
            is_web_story=item_data.get('is_web_story'),
            description=item_data.get('description'),
            pre_snippet=item_data.get('pre_snippet'),
            extended_snippet=item_data.get('extended_snippet'),
            images=item_data.get('images'),
            amp_version=item_data.get('amp_version'),
            rating=item_data.get('rating'),
            price=item_data.get('price'),
            highlighted=item_data.get('highlighted'),
            links=links,
            faq=item_data.get('faq'),
            extended_people_also_search=item_data.get('extended_people_also_search'),
            about_this_result=item_data.get('about_this_result'),
            related_result=item_data.get('related_result'),
            timestamp=item_data.get('timestamp'),
            rectangle=item_data.get('rectangle'),
            items=item_data.get('items')  # For related_searches
        )
        items.append(item)
    
    return SerpResult(
        keyword=raw_result['keyword'],
        type=raw_result['type'],
        se_domain=raw_result['se_domain'],
        location_code=raw_result['location_code'],
        language_code=raw_result['language_code'],
        check_url=raw_result['check_url'],
        datetime=raw_result['datetime'],
        spell=raw_result.get('spell'),
        refinement_chips=raw_result.get('refinement_chips'),
        item_types=raw_result['item_types'],
        se_results_count=raw_result['se_results_count'],
        items_count=raw_result['items_count'],
        items=items
    )