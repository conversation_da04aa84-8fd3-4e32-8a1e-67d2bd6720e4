"""
Simple MCP Client for DataForSEO
A clean, LLM-driven approach to calling DataForSEO MCP tools
"""

import json
import subprocess
import os
from typing import Dict, Any
from smolagents import tool
import logging

logger = logging.getLogger(__name__)

class SimpleMCPClient:
    """Simple MCP client that communicates with DataForSEO MCP server"""

    def __init__(self, username: str = None, password: str = None):
        self.username = username or os.getenv('DATAFORSEO_USERNAME')
        self.password = password or os.getenv('DATAFORSEO_PASSWORD')

        if not self.username or not self.password:
            logger.warning("DataForSEO credentials not provided. Some features may not work.")

    def call_mcp_tool(self, tool_type: str, **kwargs) -> Dict[str, Any]:
        """
        Call a specific MCP tool with parameters

        Args:
            tool_type: The type of MCP tool to call (e.g., 'dataforseo_serp', 'dataforseo_keywords_data')
            **kwargs: Parameters for the tool

        Returns:
            Dict containing the tool response
        """
        try:
            # Prepare the request
            request = {
                "type": tool_type,
                **kwargs
            }

            # Prepare credentials config
            config = {
                "username": self.username,
                "password": self.password
            }

            # Call the MCP server
            cmd = [
                'npx', '@skobyn/mcp-dataforseo',
                '--config', json.dumps(config)
            ]

            # Start the process
            process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # Send request and get response
            stdout, stderr = process.communicate(input=json.dumps(request) + '\n')

            if process.returncode != 0:
                return {
                    "error": f"MCP server error: {stderr}",
                    "status": "error"
                }

            # Parse response
            lines = stdout.strip().split('\n')
            for line in lines:
                if line.strip():
                    try:
                        response = json.loads(line)
                        if response.get('type') != 'initialize':
                            return response
                    except json.JSONDecodeError:
                        continue

            return {"error": "No valid response from MCP server", "status": "error"}

        except Exception as e:
            return {"error": f"Failed to call MCP tool: {str(e)}", "status": "error"}

# Global MCP client instance
_mcp_client = None

def get_mcp_client() -> SimpleMCPClient:
    """Get or create the global MCP client instance"""
    global _mcp_client
    if _mcp_client is None:
        _mcp_client = SimpleMCPClient()
    return _mcp_client

# Individual MCP tool functions that can be called by the LLM
@tool
def dataforseo_serp_analysis(keyword: str, location: str = "United States", device: str = "desktop") -> Dict[str, Any]:
    """
    Get SERP (Search Engine Results Page) analysis for a keyword

    Args:
        keyword: The keyword to analyze
        location: Geographic location for the search (default: "United States")
        device: Device type for the search (default: "desktop")

    Returns:
        Dict containing SERP analysis results
    """
    client = get_mcp_client()
    return client.call_mcp_tool(
        "dataforseo_serp",
        keyword=keyword,
        location_code=2840,  # US location code
        language_code="en",
        device=device,
        os="windows"
    )

@tool
def dataforseo_keyword_research(keywords: list, location: str = "United States") -> Dict[str, Any]:
    """
    Get keyword research data including search volume, difficulty, and related metrics

    Args:
        keywords: List of keywords to analyze
        location: Geographic location for the analysis (default: "United States")

    Returns:
        Dict containing keyword research results
    """
    client = get_mcp_client()
    return client.call_mcp_tool(
        "dataforseo_keywords_data",
        keywords=keywords,
        location_code=2840,  # US location code
        language_code="en"
    )

@tool
def dataforseo_backlink_analysis(target_domain: str, limit: int = 100) -> Dict[str, Any]:
    """
    Get backlink analysis for a domain

    Args:
        target_domain: The domain to analyze
        limit: Maximum number of backlinks to return (default: 100)

    Returns:
        Dict containing backlink analysis results
    """
    client = get_mcp_client()
    return client.call_mcp_tool(
        "dataforseo_backlinks",
        target=target_domain,
        limit=limit
    )

@tool
def dataforseo_onpage_analysis(url: str, check_spell: bool = True, enable_javascript: bool = True) -> Dict[str, Any]:
    """
    Get on-page SEO analysis for a URL

    Args:
        url: The URL to analyze
        check_spell: Whether to check spelling (default: True)
        enable_javascript: Whether to enable JavaScript rendering (default: True)

    Returns:
        Dict containing on-page analysis results
    """
    client = get_mcp_client()
    return client.call_mcp_tool(
        "dataforseo_onpage",
        url=url,
        check_spell=check_spell,
        enable_javascript=enable_javascript
    )

@tool
def dataforseo_domain_analysis(domain: str) -> Dict[str, Any]:
    """
    Get domain analytics and WHOIS information

    Args:
        domain: The domain to analyze

    Returns:
        Dict containing domain analysis results
    """
    client = get_mcp_client()
    return client.call_mcp_tool(
        "dataforseo_domain_analytics",
        domain=domain
    )

@tool
def dataforseo_competitor_keywords(target_domain: str, location: str = "United States", limit: int = 100) -> Dict[str, Any]:
    """
    Get keywords that a competitor domain ranks for

    Args:
        target_domain: The competitor domain to analyze
        location: Geographic location for the analysis (default: "United States")
        limit: Maximum number of keywords to return (default: 100)

    Returns:
        Dict containing competitor keyword analysis
    """
    client = get_mcp_client()
    return client.call_mcp_tool(
        "dataforseo_ranked_keywords",
        target=target_domain,
        location_code=2840,  # US location code
        language_code="en",
        limit=limit
    )

@tool
def autonomous_mcp_call(request: str) -> Dict[str, Any]:
    """
    Let the LLM intelligently choose and call the appropriate DataForSEO MCP tool based on the request.
    This is the main entry point that leverages the LLM's understanding to route requests.

    Args:
        request: Natural language request for SEO data

    Returns:
        Dict containing the analysis and results
    """
    # This tool serves as documentation for the LLM about available MCP tools
    # The LLM should analyze the request and call the appropriate specific tool above

    available_tools = {
        "dataforseo_serp_analysis": "For SERP analysis and search results",
        "dataforseo_keyword_research": "For keyword metrics, search volume, difficulty",
        "dataforseo_backlink_analysis": "For backlink analysis of domains",
        "dataforseo_onpage_analysis": "For on-page SEO analysis of URLs",
        "dataforseo_domain_analysis": "For domain analytics and WHOIS data",
        "dataforseo_competitor_keywords": "For finding keywords competitors rank for"
    }

    return {
        "message": "Please analyze the request and call the appropriate specific DataForSEO tool",
        "request": request,
        "available_tools": available_tools,
        "instruction": "Use the specific tool that best matches the user's request rather than this general function"
    }
