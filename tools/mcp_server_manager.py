#!/usr/bin/env python3
"""
MCP Server Manager
Handles starting, stopping, and managing the DataForSEO MCP server lifecycle
"""

import os
import sys
import time
import signal
import atexit
import subprocess
import json
from typing import Optional
import logging

logger = logging.getLogger(__name__)

class MCPServerManager:
    """Manages the lifecycle of MCP servers"""
    
    def __init__(self):
        self.servers = {}
        self.setup_cleanup()
    
    def setup_cleanup(self):
        """Setup cleanup handlers"""
        atexit.register(self.cleanup_all_servers)
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, cleaning up servers...")
        self.cleanup_all_servers()
        sys.exit(0)
    
    def start_dataforseo_server(self, username: str = None, password: str = None) -> bool:
        """
        Start the DataForSEO MCP server
        
        Args:
            username: <PERSON>ForSEO username
            password: DataForSEO password
            
        Returns:
            bool: True if server started successfully
        """
        if 'dataforseo' in self.servers:
            # Check if still running
            if self.servers['dataforseo'].poll() is None:
                logger.info("DataForSEO MCP server already running")
                return True
            else:
                # Process died, remove it
                del self.servers['dataforseo']
        
        # Get credentials
        username = username or os.getenv('DATAFORSEO_USERNAME')
        password = password or os.getenv('DATAFORSEO_PASSWORD')
        
        if not username or not password:
            logger.error("DataForSEO credentials not provided")
            return False
        
        try:
            # Prepare config
            config = {
                "username": username,
                "password": password
            }
            
            # Start server
            cmd = [
                'npx', '@skobyn/mcp-dataforseo',
                '--config', json.dumps(config)
            ]
            
            logger.info("Starting DataForSEO MCP server...")
            process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1
            )
            
            # Wait a moment for startup
            time.sleep(2)
            
            # Check if process is still running
            if process.poll() is None:
                self.servers['dataforseo'] = process
                logger.info("DataForSEO MCP server started successfully")
                return True
            else:
                stderr_output = process.stderr.read()
                logger.error(f"DataForSEO MCP server failed to start: {stderr_output}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to start DataForSEO MCP server: {e}")
            return False
    
    def stop_server(self, server_name: str):
        """Stop a specific server"""
        if server_name in self.servers:
            process = self.servers[server_name]
            try:
                process.terminate()
                process.wait(timeout=5)
                logger.info(f"Stopped {server_name} MCP server")
            except subprocess.TimeoutExpired:
                process.kill()
                logger.warning(f"Force killed {server_name} MCP server")
            finally:
                del self.servers[server_name]
    
    def cleanup_all_servers(self):
        """Stop all running servers"""
        for server_name in list(self.servers.keys()):
            self.stop_server(server_name)
    
    def get_server_status(self) -> dict:
        """Get status of all servers"""
        status = {}
        for name, process in self.servers.items():
            if process.poll() is None:
                status[name] = "running"
            else:
                status[name] = "stopped"
        return status
    
    def is_server_running(self, server_name: str) -> bool:
        """Check if a specific server is running"""
        if server_name not in self.servers:
            return False
        return self.servers[server_name].poll() is None

# Global server manager instance
_server_manager = None

def get_server_manager() -> MCPServerManager:
    """Get or create the global server manager"""
    global _server_manager
    if _server_manager is None:
        _server_manager = MCPServerManager()
    return _server_manager

def ensure_dataforseo_server_running() -> bool:
    """Ensure DataForSEO MCP server is running"""
    manager = get_server_manager()
    if not manager.is_server_running('dataforseo'):
        return manager.start_dataforseo_server()
    return True

if __name__ == "__main__":
    # Test the server manager
    logging.basicConfig(level=logging.INFO)
    
    manager = get_server_manager()
    
    print("🚀 Testing MCP Server Manager")
    print("=" * 40)
    
    # Check initial status
    print(f"Initial status: {manager.get_server_status()}")
    
    # Try to start DataForSEO server
    if manager.start_dataforseo_server():
        print("✅ DataForSEO server started")
        print(f"Status: {manager.get_server_status()}")
        
        # Wait a bit
        time.sleep(3)
        
        # Stop server
        manager.stop_server('dataforseo')
        print("🛑 DataForSEO server stopped")
        print(f"Final status: {manager.get_server_status()}")
    else:
        print("❌ Failed to start DataForSEO server")
        print("Make sure you have:")
        print("1. Node.js installed")
        print("2. @skobyn/mcp-dataforseo package installed")
        print("3. DATAFORSEO_USERNAME and DATAFORSEO_PASSWORD environment variables set")
