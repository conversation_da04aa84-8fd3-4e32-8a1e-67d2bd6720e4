#!/usr/bin/env python3
"""
Comprehensive MCP Client for DataForSEO
Uses the more comprehensive dataforseo-mcp-server by <PERSON><PERSON>byn
"""

import json
import subprocess
import os
import time
import atexit
from typing import Dict, Any, Optional
from smolagents import tool
import logging

logger = logging.getLogger(__name__)

class ComprehensiveMCPClient:
    """Client for the comprehensive DataForSEO MCP server"""
    
    def __init__(self, login: str = None, password: str = None):
        self.login = login or os.getenv('DATAFORSEO_LOGIN')
        self.password = password or os.getenv('DATAFORSEO_PASSWORD')
        self.process = None
        self.is_connected = False
        
        if not self.login or not self.password:
            logger.warning("DataForSEO credentials not provided. Set DATAFORSEO_LOGIN and DATAFORSEO_PASSWORD")
        
        # Setup cleanup
        atexit.register(self.cleanup)
    
    def start_server(self) -> bool:
        """Start the comprehensive MCP server"""
        if self.process and self.process.poll() is None:
            return True  # Already running
        
        try:
            # Check if the server is available
            check_cmd = ['node', '--version']
            result = subprocess.run(check_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                logger.error("Node.js not found. Please install Node.js to use the MCP server.")
                return False
            
            # Start the comprehensive MCP server
            # Note: You'll need to clone and build the server first
            server_path = os.path.join(os.path.dirname(__file__), '..', 'dataforseo-mcp-server', 'dist', 'index.js')
            
            if not os.path.exists(server_path):
                logger.error(f"MCP server not found at {server_path}")
                logger.error("Please clone and build the comprehensive server:")
                logger.error("git clone https://github.com/Skobyn/dataforseo-mcp-server.git")
                logger.error("cd dataforseo-mcp-server && npm install && npm run build")
                return False
            
            env = os.environ.copy()
            env['DATAFORSEO_LOGIN'] = self.login
            env['DATAFORSEO_PASSWORD'] = self.password
            
            self.process = subprocess.Popen(
                ['node', server_path],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env,
                bufsize=1
            )
            
            # Wait for server to start
            time.sleep(2)
            
            if self.process.poll() is None:
                self.is_connected = True
                logger.info("Comprehensive MCP server started successfully")
                return True
            else:
                stderr_output = self.process.stderr.read()
                logger.error(f"MCP server failed to start: {stderr_output}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to start MCP server: {e}")
            return False
    
    def call_tool(self, tool_name: str, **kwargs) -> Dict[str, Any]:
        """
        Call a tool on the MCP server
        
        Args:
            tool_name: Name of the tool to call
            **kwargs: Parameters for the tool
            
        Returns:
            Dict containing the tool response
        """
        if not self.is_connected:
            if not self.start_server():
                return {"error": "Failed to start MCP server", "status": "error"}
        
        try:
            # Prepare the MCP request
            request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": kwargs
                }
            }
            
            # Send request
            self.process.stdin.write(json.dumps(request) + '\n')
            self.process.stdin.flush()
            
            # Read response
            response_line = self.process.stdout.readline()
            if not response_line:
                return {"error": "No response from MCP server", "status": "error"}
            
            response = json.loads(response_line.strip())
            
            # Extract the actual result
            if 'result' in response and 'content' in response['result']:
                content = response['result']['content']
                if content and len(content) > 0:
                    return json.loads(content[0]['text'])
            
            return response
            
        except Exception as e:
            logger.error(f"MCP tool call failed: {e}")
            return {"error": f"Tool call failed: {str(e)}", "status": "error"}
    
    def cleanup(self):
        """Cleanup the MCP server process"""
        if self.process:
            self.process.terminate()
            self.process.wait()
            self.process = None
            self.is_connected = False

# Global client instance
_mcp_client = None

def get_comprehensive_mcp_client() -> ComprehensiveMCPClient:
    """Get or create the global comprehensive MCP client"""
    global _mcp_client
    if _mcp_client is None:
        _mcp_client = ComprehensiveMCPClient()
    return _mcp_client

# High-level tool functions that map to the comprehensive server's tools
@tool
def serp_google_organic_live(keyword: str, location_code: int = 2840, language_code: str = "en") -> Dict[str, Any]:
    """
    Get live Google organic search results for a keyword
    
    Args:
        keyword: The search keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")
        
    Returns:
        Dict containing SERP results
    """
    client = get_comprehensive_mcp_client()
    return client.call_tool(
        "serp_google_organic_live",
        keyword=keyword,
        location_code=location_code,
        language_code=language_code
    )

@tool
def labs_google_related_keywords(keyword: str, location_code: int = 2840, language_code: str = "en", limit: int = 10) -> Dict[str, Any]:
    """
    Get related keywords for a given keyword using DataForSEO Labs
    
    Args:
        keyword: The seed keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")
        limit: Number of related keywords to return (default: 10)
        
    Returns:
        Dict containing related keywords with metrics
    """
    client = get_comprehensive_mcp_client()
    return client.call_tool(
        "labs_google_related_keywords",
        keyword=keyword,
        location_code=location_code,
        language_code=language_code,
        limit=limit
    )

@tool
def labs_google_keyword_ideas(keyword: str, location_code: int = 2840, language_code: str = "en", limit: int = 100) -> Dict[str, Any]:
    """
    Get keyword ideas for comprehensive keyword research
    
    Args:
        keyword: The seed keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")
        limit: Number of keyword ideas to return (default: 100)
        
    Returns:
        Dict containing keyword ideas with metrics
    """
    client = get_comprehensive_mcp_client()
    return client.call_tool(
        "labs_google_keyword_ideas",
        keyword=keyword,
        location_code=location_code,
        language_code=language_code,
        limit=limit
    )

@tool
def backlinks_summary(target: str, limit: int = 100) -> Dict[str, Any]:
    """
    Get backlinks summary for a domain
    
    Args:
        target: The target domain to analyze
        limit: Number of backlinks to include in summary (default: 100)
        
    Returns:
        Dict containing backlinks summary
    """
    client = get_comprehensive_mcp_client()
    return client.call_tool(
        "backlinks_summary",
        target=target,
        limit=limit
    )

@tool
def keywords_google_ads_search_volume(keywords: list, location_code: int = 2840, language_code: str = "en") -> Dict[str, Any]:
    """
    Get search volume data for keywords using Google Ads API
    
    Args:
        keywords: List of keywords to analyze
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")
        
    Returns:
        Dict containing search volume data
    """
    client = get_comprehensive_mcp_client()
    return client.call_tool(
        "keywords_google_ads_search_volume",
        keywords=keywords,
        location_code=location_code,
        language_code=language_code
    )
