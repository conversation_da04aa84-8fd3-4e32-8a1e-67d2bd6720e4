#!/usr/bin/env python3
"""
Direct DataForSEO API Client
A simple, working client that calls DataForSEO API directly
This bypasses MCP complexity while maintaining the LLM-driven tool selection approach
"""

import os
import requests
import json
import base64
from typing import Dict, Any, List
from smolagents import tool
import logging

# Try to load .env file if available
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

logger = logging.getLogger(__name__)

class DirectDataForSEOClient:
    """Direct client for DataForSEO API"""
    
    def __init__(self, username: str = None, password: str = None):
        # Support both LOGIN and USERNAME formats
        self.username = username or os.getenv('DATAFORSEO_LOGIN') or os.getenv('DATAFORSEO_USERNAME')
        self.password = password or os.getenv('DATAFORSEO_PASSWORD')
        
        if not self.username or not self.password:
            logger.warning("DataForSEO credentials not provided. Set DATAFORSEO_USERNAME/DATAFORSEO_PASSWORD")
            return
        
        # Create auth header
        credentials = f"{self.username}:{self.password}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()
        
        self.headers = {
            'Authorization': f'Basic {encoded_credentials}',
            'Content-Type': 'application/json'
        }
        
        self.base_url = 'https://api.dataforseo.com/v3'
        
        logger.info("DataForSEO client initialized successfully")
    
    def _make_request(self, endpoint: str, data: List[Dict]) -> Dict[str, Any]:
        """Make a request to DataForSEO API"""
        if not self.username or not self.password:
            return {
                "error": "DataForSEO credentials not configured",
                "status": "error"
            }
        
        try:
            url = f"{self.base_url}{endpoint}"
            response = requests.post(url, headers=self.headers, json=data)
            
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    "error": f"API request failed with status {response.status_code}",
                    "details": response.text,
                    "status": "error"
                }
                
        except Exception as e:
            return {
                "error": f"Request failed: {str(e)}",
                "status": "error"
            }

# Global client instance
_client = None

def get_dataforseo_client() -> DirectDataForSEOClient:
    """Get or create the global DataForSEO client"""
    global _client
    if _client is None:
        _client = DirectDataForSEOClient()
    return _client

# LLM-callable tools using direct API calls
@tool
def serp_google_organic_live(keyword: str, location_code: int = 2840, language_code: str = "en", device: str = "desktop") -> Dict[str, Any]:
    """
    Get live Google organic search results for a keyword
    
    Args:
        keyword: The search keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")
        device: Device type (default: "desktop")
        
    Returns:
        Dict containing SERP results
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_code": location_code,
        "language_code": language_code,
        "device": device,
        "os": "windows"
    }]
    
    return client._make_request('/serp/google/organic/live/advanced', data)

@tool
def labs_google_related_keywords(keyword: str, location_code: int = 2840, language_code: str = "en", limit: int = 10) -> Dict[str, Any]:
    """
    Get related keywords for a given keyword using DataForSEO Labs
    
    Args:
        keyword: The seed keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")
        limit: Number of related keywords to return (default: 10)
        
    Returns:
        Dict containing related keywords with metrics
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_code": location_code,
        "language_code": language_code,
        "limit": limit
    }]
    
    return client._make_request('/dataforseo_labs/google/related_keywords/live', data)

@tool
def labs_google_keyword_ideas(keyword: str, location_code: int = 2840, language_code: str = "en", limit: int = 100) -> Dict[str, Any]:
    """
    Get keyword ideas for comprehensive keyword research
    
    Args:
        keyword: The seed keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")
        limit: Number of keyword ideas to return (default: 100)
        
    Returns:
        Dict containing keyword ideas with metrics
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_code": location_code,
        "language_code": language_code,
        "limit": limit
    }]
    
    return client._make_request('/dataforseo_labs/google/keyword_ideas/live', data)

@tool
def backlinks_summary(target: str, limit: int = 100) -> Dict[str, Any]:
    """
    Get backlinks summary for a domain
    
    Args:
        target: The target domain to analyze
        limit: Number of backlinks to include in summary (default: 100)
        
    Returns:
        Dict containing backlinks summary
    """
    client = get_dataforseo_client()
    data = [{
        "target": target,
        "limit": limit
    }]
    
    return client._make_request('/backlinks/summary/live', data)

@tool
def keywords_google_ads_search_volume(keywords: List[str], location_code: int = 2840, language_code: str = "en") -> Dict[str, Any]:
    """
    Get search volume data for keywords using Google Ads API
    
    Args:
        keywords: List of keywords to analyze
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")
        
    Returns:
        Dict containing search volume data
    """
    client = get_dataforseo_client()
    data = [{
        "keywords": keywords,
        "location_code": location_code,
        "language_code": language_code
    }]
    
    return client._make_request('/keywords_data/google_ads/search_volume/live', data)

@tool
def labs_google_domain_rank_overview(target: str, location_code: int = 2840, language_code: str = "en") -> Dict[str, Any]:
    """
    Get domain ranking overview and metrics
    
    Args:
        target: The target domain to analyze
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")
        
    Returns:
        Dict containing domain ranking overview
    """
    client = get_dataforseo_client()
    data = [{
        "target": target,
        "location_code": location_code,
        "language_code": language_code
    }]
    
    return client._make_request('/dataforseo_labs/google/domain_rank_overview/live', data)

@tool
def labs_google_ranked_keywords(target: str, location_code: int = 2840, language_code: str = "en", limit: int = 100) -> Dict[str, Any]:
    """
    Get keywords that a domain ranks for (competitor analysis)
    
    Args:
        target: The target domain to analyze
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")
        limit: Number of keywords to return (default: 100)
        
    Returns:
        Dict containing keywords the domain ranks for
    """
    client = get_dataforseo_client()
    data = [{
        "target": target,
        "location_code": location_code,
        "language_code": language_code,
        "limit": limit
    }]
    
    return client._make_request('/dataforseo_labs/google/ranked_keywords/live', data)

# ============================================================================
# FLEXIBLE DATAFORSEO API CALLER - LLM can call ANY endpoint
# ============================================================================

@tool
def call_dataforseo_api_endpoint(
    api_endpoint_path: str,
    keyword: str = None,
    keywords: List[str] = None,
    target_domain: str = None,
    target_url: str = None,
    location_code: int = 2840,
    language_code: str = "en",
    device_type: str = "desktop",
    operating_system: str = "windows",
    search_limit: int = 100,
    additional_parameters: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    Call any DataForSEO API endpoint with comprehensive parameter support.
    This flexible tool allows access to 100+ DataForSEO API endpoints for SERP analysis,
    keyword research, backlink analysis, on-page SEO, domain analytics, and more.

    Args:
        api_endpoint_path: DataForSEO API endpoint (e.g., '/serp/google/organic/live/advanced',
                          '/dataforseo_labs/google/related_keywords/live', '/backlinks/summary/live')
        keyword: Single keyword for SERP analysis or keyword research (e.g., 'digital marketing')
        keywords: List of keywords for bulk operations (e.g., ['seo', 'marketing', 'analytics'])
        target_domain: Domain to analyze (e.g., 'semrush.com', 'ahrefs.com')
        target_url: Specific URL to analyze (e.g., 'https://example.com/page')
        location_code: Geographic location code (2840=USA, 2826=UK, 2276=Germany, 1023191=New York)
        language_code: Language code (en=English, es=Spanish, fr=French, de=German)
        device_type: Device for SERP analysis (desktop, mobile, tablet)
        operating_system: OS for SERP analysis (windows, macos, linux, android, ios)
        search_limit: Maximum number of results to return (1-1000, default 100)
        additional_parameters: Any other endpoint-specific parameters as a dictionary

    Returns:
        Dict containing the complete API response with status, tasks, and results

    Common Endpoints:
        SERP Analysis:
        - '/serp/google/organic/live/advanced' - Google search results
        - '/serp/google/maps/live/advanced' - Google Maps results
        - '/serp/bing/organic/live/advanced' - Bing search results

        Keyword Research:
        - '/dataforseo_labs/google/related_keywords/live' - Related keywords with metrics
        - '/dataforseo_labs/google/keyword_ideas/live' - Keyword suggestions
        - '/dataforseo_labs/google/bulk_keyword_difficulty/live' - Keyword difficulty scores
        - '/keywords_data/google_ads/search_volume/live' - Search volume data

        Competitor Analysis:
        - '/dataforseo_labs/google/ranked_keywords/live' - Keywords a domain ranks for
        - '/dataforseo_labs/google/domain_rank_overview/live' - Domain ranking metrics

        Backlink Analysis:
        - '/backlinks/summary/live' - Backlink summary for domain
        - '/backlinks/anchors/live' - Anchor text analysis
        - '/backlinks/referring_domains/live' - Referring domains

        On-Page SEO:
        - '/on_page/summary/live' - On-page SEO summary
        - '/on_page/pages/live' - Page-level analysis
        - '/on_page/lighthouse/live' - Google Lighthouse scores

        Domain Analytics:
        - '/domain_analytics/google/overview/live' - Domain overview
        - '/domain_analytics/google/technologies/live' - Technologies used
        - '/domain_analytics/google/whois/live' - WHOIS information
    """
    client = get_dataforseo_client()

    # Ensure endpoint starts with /
    if not api_endpoint_path.startswith('/'):
        api_endpoint_path = '/' + api_endpoint_path

    # Build parameters dictionary based on provided arguments
    parameters = {}

    # Add keyword parameters
    if keyword is not None:
        parameters['keyword'] = keyword
    if keywords is not None:
        parameters['keywords'] = keywords

    # Add target parameters
    if target_domain is not None:
        parameters['target'] = target_domain
    if target_url is not None:
        parameters['target'] = target_url

    # Add location and language
    parameters['location_code'] = location_code
    parameters['language_code'] = language_code

    # Add device and OS for SERP endpoints
    if 'serp' in api_endpoint_path:
        parameters['device'] = device_type
        parameters['os'] = operating_system

    # Add limit for endpoints that support it
    if any(endpoint_type in api_endpoint_path for endpoint_type in ['related_keywords', 'keyword_ideas', 'ranked_keywords']):
        parameters['limit'] = search_limit

    # Add any additional parameters
    if additional_parameters:
        parameters.update(additional_parameters)

    # Create data payload - DataForSEO endpoints expect an array with one object
    data = [parameters]

    return client._make_request(api_endpoint_path, data)

# ============================================================================
# DOCUMENTATION-AWARE DATAFORSEO CALLER - Prevents LLM Hallucination
# ============================================================================

@tool
def query_dataforseo_with_documentation(
    user_query: str,
    target_keyword: str = None,
    target_keywords: List[str] = None,
    target_domain: str = None,
    location_name: str = "United States",
    language: str = "English",
    result_limit: int = 100
) -> Dict[str, Any]:
    """
    Intelligent DataForSEO query tool that uses comprehensive API documentation to prevent hallucination.
    This tool analyzes the user's natural language query, finds the best matching DataForSEO endpoint,
    validates parameters, and makes the appropriate API call.

    Args:
        user_query: Natural language description of what SEO data you need (e.g., "find related keywords for content marketing", "get SERP results for digital marketing", "analyze backlinks for semrush.com")
        target_keyword: Single keyword for analysis (e.g., "digital marketing", "seo tools")
        target_keywords: List of keywords for bulk analysis (e.g., ["seo", "marketing", "analytics"])
        target_domain: Domain to analyze (e.g., "semrush.com", "ahrefs.com", "example.com")
        location_name: Geographic location for analysis ("United States", "United Kingdom", "Germany", "New York")
        language: Language for analysis ("English", "Spanish", "French", "German")
        result_limit: Maximum number of results to return (1-1000, default 100)

    Returns:
        Dict containing:
        - endpoint_info: Information about the selected endpoint and reasoning
        - api_response: The actual DataForSEO API response
        - documentation: Relevant documentation for the endpoint used

    Examples of user_query:
        - "Find related keywords for 'content marketing'"
        - "Get search volume for these keywords: seo, marketing, analytics"
        - "What keywords does semrush.com rank for?"
        - "Analyze Google search results for 'digital marketing tools'"
        - "Check keyword difficulty for 'seo tools', 'keyword research'"
        - "Get backlink summary for ahrefs.com"
        - "Analyze on-page SEO for example.com"
        - "Get domain overview for shopify.com"
    """
    from .dataforseo_documentation import find_best_endpoint, get_endpoint_documentation, validate_parameters, DATAFORSEO_API_DOCUMENTATION

    # Find the best endpoint based on user query
    endpoint_match = find_best_endpoint(user_query)
    endpoint = endpoint_match["endpoint"]

    # Get documentation for the endpoint
    endpoint_doc = get_endpoint_documentation(endpoint)

    # Build parameters based on the endpoint requirements and user inputs
    parameters = {}

    # Map location name to location code
    location_codes = DATAFORSEO_API_DOCUMENTATION["common_parameters"]["location_codes"]
    location_code = 2840  # Default to US
    for code, name in location_codes.items():
        if location_name.lower() in name.lower():
            location_code = int(code)
            break

    # Map language name to language code
    language_codes = DATAFORSEO_API_DOCUMENTATION["common_parameters"]["language_codes"]
    language_code = "en"  # Default to English
    for code, name in language_codes.items():
        if language.lower() in name.lower():
            language_code = code
            break

    # Set parameters based on endpoint requirements
    if "keyword" in endpoint_match["required_params"]:
        if target_keyword:
            parameters["keyword"] = target_keyword
        elif target_keywords and len(target_keywords) > 0:
            parameters["keyword"] = target_keywords[0]
        else:
            # Try to extract keyword from user query
            import re
            keyword_match = re.search(r"['\"]([^'\"]+)['\"]", user_query)
            if keyword_match:
                parameters["keyword"] = keyword_match.group(1)
            else:
                return {
                    "error": "Keyword required but not provided",
                    "endpoint_info": endpoint_match,
                    "suggestion": "Please provide target_keyword parameter"
                }

    if "keywords" in endpoint_match["required_params"]:
        if target_keywords:
            parameters["keywords"] = target_keywords
        elif target_keyword:
            parameters["keywords"] = [target_keyword]
        else:
            return {
                "error": "Keywords list required but not provided",
                "endpoint_info": endpoint_match,
                "suggestion": "Please provide target_keywords parameter"
            }

    if "target" in endpoint_match["required_params"]:
        if target_domain:
            parameters["target"] = target_domain
        else:
            # Try to extract domain from user query
            import re
            domain_match = re.search(r"([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})", user_query)
            if domain_match:
                parameters["target"] = domain_match.group(1)
            else:
                return {
                    "error": "Target domain required but not provided",
                    "endpoint_info": endpoint_match,
                    "suggestion": "Please provide target_domain parameter"
                }

    # Add common parameters
    parameters["location_code"] = location_code
    parameters["language_code"] = language_code

    # Add endpoint-specific parameters
    if "limit" in endpoint_doc.get("optional_params", []):
        parameters["limit"] = result_limit

    # Add suggested parameters from documentation
    parameters.update(endpoint_match.get("suggested_params", {}))

    # Validate parameters
    validation = validate_parameters(endpoint, parameters)
    if not validation["valid"]:
        return {
            "error": validation["error"],
            "endpoint_info": endpoint_match,
            "validation_details": validation
        }

    # Make the API call
    client = get_dataforseo_client()
    api_response = client._make_request(endpoint, [parameters])

    return {
        "endpoint_info": {
            "endpoint": endpoint,
            "category": endpoint_match["category"],
            "reasoning": endpoint_match["reasoning"],
            "parameters_used": parameters
        },
        "api_response": api_response,
        "documentation": {
            "description": endpoint_doc["description"],
            "use_cases": endpoint_doc["use_cases"],
            "example": endpoint_doc["example"]
        }
    }

# ============================================================================
# SERP API - Additional Search Engines and Types
# ============================================================================

@tool
def serp_google_maps_live(keyword: str, location_code: int = 2840, language_code: str = "en") -> Dict[str, Any]:
    """
    Get live Google Maps search results

    Args:
        keyword: The search keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")

    Returns:
        Dict containing Google Maps results
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_code": location_code,
        "language_code": language_code
    }]

    return client._make_request('/serp/google/maps/live/advanced', data)

@tool
def serp_google_images_live(keyword: str, location_code: int = 2840, language_code: str = "en") -> Dict[str, Any]:
    """
    Get live Google Images search results

    Args:
        keyword: The search keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")

    Returns:
        Dict containing Google Images results
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_code": location_code,
        "language_code": language_code
    }]

    return client._make_request('/serp/google/images/live/advanced', data)

@tool
def serp_google_news_live(keyword: str, location_code: int = 2840, language_code: str = "en") -> Dict[str, Any]:
    """
    Get live Google News search results

    Args:
        keyword: The search keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")

    Returns:
        Dict containing Google News results
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_code": location_code,
        "language_code": language_code
    }]

    return client._make_request('/serp/google/news/live/advanced', data)

@tool
def serp_bing_organic_live(keyword: str, location_code: int = 2840, language_code: str = "en") -> Dict[str, Any]:
    """
    Get live Bing organic search results

    Args:
        keyword: The search keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")

    Returns:
        Dict containing Bing search results
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_code": location_code,
        "language_code": language_code
    }]

    return client._make_request('/serp/bing/organic/live/advanced', data)

# Additional SERP API Tools
@tool
def serp_google_maps_live(keyword: str, location_code: int = 2840, language_code: str = "en") -> Dict[str, Any]:
    """
    Get live Google Maps search results

    Args:
        keyword: The search keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")

    Returns:
        Dict containing Google Maps results
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_code": location_code,
        "language_code": language_code
    }]

    return client._make_request('/serp/google/maps/live/advanced', data)

@tool
def serp_google_images_live(keyword: str, location_code: int = 2840, language_code: str = "en") -> Dict[str, Any]:
    """
    Get live Google Images search results

    Args:
        keyword: The search keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")

    Returns:
        Dict containing Google Images results
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_code": location_code,
        "language_code": language_code
    }]

    return client._make_request('/serp/google/images/live/advanced', data)

@tool
def serp_google_news_live(keyword: str, location_code: int = 2840, language_code: str = "en") -> Dict[str, Any]:
    """
    Get live Google News search results

    Args:
        keyword: The search keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")

    Returns:
        Dict containing Google News results
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_code": location_code,
        "language_code": language_code
    }]

    return client._make_request('/serp/google/news/live/advanced', data)
