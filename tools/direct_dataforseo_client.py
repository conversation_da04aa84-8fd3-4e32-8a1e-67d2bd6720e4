#!/usr/bin/env python3
"""
Direct DataForSEO API Client
A simple, working client that calls DataForSEO API directly
This bypasses MCP complexity while maintaining the LLM-driven tool selection approach
"""

import os
import requests
import json
import base64
from typing import Dict, Any, List
from smolagents import tool
import logging

# Try to load .env file if available
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

logger = logging.getLogger(__name__)

class DirectDataForSEOClient:
    """Direct client for DataForSEO API"""
    
    def __init__(self, username: str = None, password: str = None):
        # Support both LOGIN and USERNAME formats
        self.username = username or os.getenv('DATAFORSEO_LOGIN') or os.getenv('DATAFORSEO_USERNAME')
        self.password = password or os.getenv('DATAFORSEO_PASSWORD')
        
        if not self.username or not self.password:
            logger.warning("DataForSEO credentials not provided. Set DATAFORSEO_USERNAME/DATAFORSEO_PASSWORD")
            return
        
        # Create auth header
        credentials = f"{self.username}:{self.password}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()
        
        self.headers = {
            'Authorization': f'Basic {encoded_credentials}',
            'Content-Type': 'application/json'
        }
        
        self.base_url = 'https://api.dataforseo.com/v3'
        
        logger.info("DataForSEO client initialized successfully")
    
    def _make_request(self, endpoint: str, data: List[Dict]) -> Dict[str, Any]:
        """Make a request to DataForSEO API"""
        if not self.username or not self.password:
            return {
                "error": "DataForSEO credentials not configured",
                "status": "error"
            }
        
        try:
            url = f"{self.base_url}{endpoint}"
            response = requests.post(url, headers=self.headers, json=data)
            
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    "error": f"API request failed with status {response.status_code}",
                    "details": response.text,
                    "status": "error"
                }
                
        except Exception as e:
            return {
                "error": f"Request failed: {str(e)}",
                "status": "error"
            }

# Global client instance
_client = None

def get_dataforseo_client() -> DirectDataForSEOClient:
    """Get or create the global DataForSEO client"""
    global _client
    if _client is None:
        _client = DirectDataForSEOClient()
    return _client

# LLM-callable tools using direct API calls
@tool
def serp_google_organic_live(keyword: str, location_code: int = 2840, language_code: str = "en", device: str = "desktop") -> Dict[str, Any]:
    """
    Get live Google organic search results for a keyword
    
    Args:
        keyword: The search keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")
        device: Device type (default: "desktop")
        
    Returns:
        Dict containing SERP results
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_code": location_code,
        "language_code": language_code,
        "device": device,
        "os": "windows"
    }]
    
    return client._make_request('/serp/google/organic/live/advanced', data)

@tool
def labs_google_related_keywords(keyword: str, location_code: int = 2840, language_code: str = "en", limit: int = 10) -> Dict[str, Any]:
    """
    Get related keywords for a given keyword using DataForSEO Labs
    
    Args:
        keyword: The seed keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")
        limit: Number of related keywords to return (default: 10)
        
    Returns:
        Dict containing related keywords with metrics
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_code": location_code,
        "language_code": language_code,
        "limit": limit
    }]
    
    return client._make_request('/dataforseo_labs/google/related_keywords/live', data)

@tool
def labs_google_keyword_ideas(keyword: str, location_code: int = 2840, language_code: str = "en", limit: int = 100) -> Dict[str, Any]:
    """
    Get keyword ideas for comprehensive keyword research
    
    Args:
        keyword: The seed keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")
        limit: Number of keyword ideas to return (default: 100)
        
    Returns:
        Dict containing keyword ideas with metrics
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_code": location_code,
        "language_code": language_code,
        "limit": limit
    }]
    
    return client._make_request('/dataforseo_labs/google/keyword_ideas/live', data)

@tool
def backlinks_summary(target: str, limit: int = 100) -> Dict[str, Any]:
    """
    Get backlinks summary for a domain
    
    Args:
        target: The target domain to analyze
        limit: Number of backlinks to include in summary (default: 100)
        
    Returns:
        Dict containing backlinks summary
    """
    client = get_dataforseo_client()
    data = [{
        "target": target,
        "limit": limit
    }]
    
    return client._make_request('/backlinks/summary/live', data)

@tool
def keywords_google_ads_search_volume(keywords: List[str], location_code: int = 2840, language_code: str = "en") -> Dict[str, Any]:
    """
    Get search volume data for keywords using Google Ads API
    
    Args:
        keywords: List of keywords to analyze
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")
        
    Returns:
        Dict containing search volume data
    """
    client = get_dataforseo_client()
    data = [{
        "keywords": keywords,
        "location_code": location_code,
        "language_code": language_code
    }]
    
    return client._make_request('/keywords_data/google_ads/search_volume/live', data)

@tool
def labs_google_domain_rank_overview(target: str, location_code: int = 2840, language_code: str = "en") -> Dict[str, Any]:
    """
    Get domain ranking overview and metrics
    
    Args:
        target: The target domain to analyze
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")
        
    Returns:
        Dict containing domain ranking overview
    """
    client = get_dataforseo_client()
    data = [{
        "target": target,
        "location_code": location_code,
        "language_code": language_code
    }]
    
    return client._make_request('/dataforseo_labs/google/domain_rank_overview/live', data)

@tool
def labs_google_ranked_keywords(target: str, location_code: int = 2840, language_code: str = "en", limit: int = 100) -> Dict[str, Any]:
    """
    Get keywords that a domain ranks for (competitor analysis)
    
    Args:
        target: The target domain to analyze
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")
        limit: Number of keywords to return (default: 100)
        
    Returns:
        Dict containing keywords the domain ranks for
    """
    client = get_dataforseo_client()
    data = [{
        "target": target,
        "location_code": location_code,
        "language_code": language_code,
        "limit": limit
    }]
    
    return client._make_request('/dataforseo_labs/google/ranked_keywords/live', data)

# ============================================================================
# FLEXIBLE DATAFORSEO API CALLER - LLM can call ANY endpoint
# ============================================================================

@tool
def call_dataforseo(endpoint: str, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Call any DataForSEO API endpoint with arbitrary parameters.
    This allows the LLM to access the full DataForSEO API (100+ endpoints) dynamically.

    Args:
        endpoint: The DataForSEO API endpoint path (e.g., '/serp/google/organic/live/advanced')
        parameters: Dict of parameters required by the specific endpoint

    Returns:
        Dict containing the API response

    Examples:
        # SERP analysis
        call_dataforseo('/serp/google/organic/live/advanced',
                       {'keyword': 'seo tools', 'location_code': 2840, 'language_code': 'en'})

        # Keyword difficulty
        call_dataforseo('/dataforseo_labs/google/bulk_keyword_difficulty/live',
                       {'keywords': ['seo', 'marketing'], 'location_code': 2840})

        # On-page analysis
        call_dataforseo('/on_page/summary/live',
                       {'target': 'example.com'})

        # Domain analytics
        call_dataforseo('/domain_analytics/google/overview/live',
                       {'target': 'semrush.com', 'location_code': 2840})
    """
    client = get_dataforseo_client()

    # Ensure endpoint starts with /
    if not endpoint.startswith('/'):
        endpoint = '/' + endpoint

    # Create data payload - most DataForSEO endpoints expect an array with one object
    if parameters is None:
        parameters = {}
    data = [parameters]

    return client._make_request(endpoint, data)

# ============================================================================
# SERP API - Additional Search Engines and Types
# ============================================================================

@tool
def serp_google_maps_live(keyword: str, location_code: int = 2840, language_code: str = "en") -> Dict[str, Any]:
    """
    Get live Google Maps search results

    Args:
        keyword: The search keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")

    Returns:
        Dict containing Google Maps results
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_code": location_code,
        "language_code": language_code
    }]

    return client._make_request('/serp/google/maps/live/advanced', data)

@tool
def serp_google_images_live(keyword: str, location_code: int = 2840, language_code: str = "en") -> Dict[str, Any]:
    """
    Get live Google Images search results

    Args:
        keyword: The search keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")

    Returns:
        Dict containing Google Images results
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_code": location_code,
        "language_code": language_code
    }]

    return client._make_request('/serp/google/images/live/advanced', data)

@tool
def serp_google_news_live(keyword: str, location_code: int = 2840, language_code: str = "en") -> Dict[str, Any]:
    """
    Get live Google News search results

    Args:
        keyword: The search keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")

    Returns:
        Dict containing Google News results
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_code": location_code,
        "language_code": language_code
    }]

    return client._make_request('/serp/google/news/live/advanced', data)

@tool
def serp_bing_organic_live(keyword: str, location_code: int = 2840, language_code: str = "en") -> Dict[str, Any]:
    """
    Get live Bing organic search results

    Args:
        keyword: The search keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")

    Returns:
        Dict containing Bing search results
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_code": location_code,
        "language_code": language_code
    }]

    return client._make_request('/serp/bing/organic/live/advanced', data)

# Additional SERP API Tools
@tool
def serp_google_maps_live(keyword: str, location_code: int = 2840, language_code: str = "en") -> Dict[str, Any]:
    """
    Get live Google Maps search results

    Args:
        keyword: The search keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")

    Returns:
        Dict containing Google Maps results
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_code": location_code,
        "language_code": language_code
    }]

    return client._make_request('/serp/google/maps/live/advanced', data)

@tool
def serp_google_images_live(keyword: str, location_code: int = 2840, language_code: str = "en") -> Dict[str, Any]:
    """
    Get live Google Images search results

    Args:
        keyword: The search keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")

    Returns:
        Dict containing Google Images results
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_code": location_code,
        "language_code": language_code
    }]

    return client._make_request('/serp/google/images/live/advanced', data)

@tool
def serp_google_news_live(keyword: str, location_code: int = 2840, language_code: str = "en") -> Dict[str, Any]:
    """
    Get live Google News search results

    Args:
        keyword: The search keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")

    Returns:
        Dict containing Google News results
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_code": location_code,
        "language_code": language_code
    }]

    return client._make_request('/serp/google/news/live/advanced', data)
