{"name": "dataforseo-mcp-server", "version": "1.0.0", "description": "A comprehensive stdio MCP server for DataForSEO API", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts"}, "keywords": ["mcp", "dataforseo", "api", "seo"], "author": "", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.2.2", "axios": "^1.6.7", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.11.19", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}